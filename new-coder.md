# 项目开发指南

> 📅 生成时间: 2025/1/15

## 🧠 AI 深度分析

### 📊 项目概述分析
这是一个基于 TypeScript 的 Continue AI 代码助手项目，主要用于在 IDE 中提供智能代码补全和聊天功能。

### 🏗️ 架构分析
- **前端**: React + Redux 架构，使用 TypeScript
- **后端**: Node.js 核心服务
- **扩展**: 支持 VSCode 和 IntelliJ IDEA
- **通信**: 基于消息协议的前后端通信

### ⚙️ 技术栈分析
- **语言**: TypeScript, JavaScript, Kotlin (IntelliJ 扩展)
- **框架**: React, Redux Toolkit
- **构建工具**: Vite, Webpack
- **测试**: Jest, Vitest
- **包管理**: npm, pnpm

### ⚙️ 代码风格与标准
- 使用 ESLint 和 Prettier 进行代码格式化
- 遵循 TypeScript 严格模式
- 采用函数式编程风格
- 使用 async/await 处理异步操作

## 🎯 开发重点
1. **系统消息增强**: 在聊天和代理模式下自动引入项目信息
2. **IDE 集成**: 支持多种 IDE 的无缝集成
3. **用户体验**: 提供流畅的代码补全和聊天体验
4. **扩展性**: 支持插件和自定义配置

## 📝 注意事项
- 所有用户交互默认使用中文
- 支持多种 LLM 模型
- 注重代码安全和性能优化
