import {
  BrowserSerializedContinueConfig,
  ChatHistoryItem,
  ChatMessage,
  ContextItemWithId,
  IDE,
  RuleWithSource,
  TextMessagePart,
  ToolResultChatMessage,
  UserChatMessage,
} from "../";
import { findLast } from "../util/findLast";
import { normalizeToMessageParts } from "../util/messageContent";
import { joinPathsToUri } from "../util/uri";
import { isUserOrToolMsg } from "./messages";
import { getSystemMessageWithRules } from "./rules/getSystemMessageWithRules";

export const DEFAULT_CHAT_SYSTEM_MESSAGE_URL =
  "https://github.com/continuedev/continue/blob/main/core/llm/constructMessages.ts";

export const DEFAULT_AGENT_SYSTEM_MESSAGE_URL =
  "https://github.com/continuedev/continue/blob/main/core/llm/constructMessages.ts";

export const DEFAULT_STRUCTURED_AGENT_SYSTEM_MESSAGE_URL =
  "https://github.com/continuedev/continue/blob/main/core/llm/constructMessages.ts";

/**
 * 读取项目根目录下的 new-coder.md 文件内容
 */
async function readNewCoderMd(ide: IDE): Promise<string> {
  try {
    const workspaceDirs = await ide.getWorkspaceDirs();
    if (workspaceDirs.length === 0) {
      return "";
    }

    // 尝试从第一个工作区目录读取 new-coder.md
    const newCoderPath = joinPathsToUri(workspaceDirs[0], "new-coder.md");
    const exists = await ide.fileExists(newCoderPath);

    if (exists) {
      const content = await ide.readFile(newCoderPath);
      return content.trim();
    }
  } catch (error) {
    console.warn("Failed to read new-coder.md:", error);
  }

  return "";
}

const EDIT_MESSAGE = `\
  Always include the language and file name in the info string when you write code blocks.
  If you are editing "src/main.py" for example, your code block should start with '\`\`\`python src/main.py'

  When addressing code modification requests, present a concise code snippet that
  emphasizes only the necessary changes and uses abbreviated placeholders for
  unmodified sections. For example:

  \`\`\`language /path/to/file
  // ... existing code ...

  {{ modified code here }}

  // ... existing code ...

  {{ another modification }}

  // ... rest of code ...
  \`\`\`

  In existing files, you should always restate the function or class that the snippet belongs to:

  \`\`\`language /path/to/file
  // ... existing code ...

  function exampleFunction() {
    // ... existing code ...

    {{ modified code here }}

    // ... rest of function ...
  }

  // ... rest of code ...
  \`\`\`

  Since users have access to their complete file, they prefer reading only the
  relevant modifications. It's perfectly acceptable to omit unmodified portions
  at the beginning, middle, or end of files using these "lazy" comments. Only
  provide the complete file when explicitly requested. Include a concise explanation
  of changes unless the user specifically asks for code only.
`;

export const DEFAULT_CHAT_SYSTEM_MESSAGE = `\
<important_rules>
  You are in chat mode,Answer in Chinese.

  If the user asks to make changes to files offer that they can use the Apply Button on the code block, or switch to Agent Mode to make the suggested updates automatically.
  If needed consisely explain to the user they can switch to agent mode using the Mode Selector dropdown and provide no other details.

${EDIT_MESSAGE}
</important_rules>`;

export const DEFAULT_AGENT_SYSTEM_MESSAGE = `\
<important_rules>
  You are in agent mode,Answer in Chinese.

${EDIT_MESSAGE}
</important_rules>`;

export const DEFAULT_STRUCTURED_AGENT_SYSTEM_MESSAGE = ``;

/**
 * Helper function to get the context items for a user message
 */
function getUserContextItems(
  userMsg: UserChatMessage | ToolResultChatMessage | undefined,
  history: ChatHistoryItem[],
): ContextItemWithId[] {
  if (!userMsg) return [];

  // Find the history item that contains the userMsg
  const historyItem = history.find((item) => {
    // Check if the message ID matches
    if ("id" in userMsg && "id" in item.message) {
      return (item.message as any).id === (userMsg as any).id;
    }
    // Fallback to content comparison
    return (
      item.message.content === userMsg.content &&
      item.message.role === userMsg.role
    );
  });

  return historyItem?.contextItems || [];
}

export async function constructMessages(
  messageMode: string,
  history: ChatHistoryItem[],
  baseChatOrAgentSystemMessage: string | undefined,
  rules: RuleWithSource[],
  config: BrowserSerializedContinueConfig, // 添加config参数
  dynamicSystemMessage?: string, // 添加动态系统消息参数
  ide?: IDE, // 添加IDE参数用于读取new-coder.md
): Promise<ChatMessage[]> {
  const filteredHistory = history.filter(
    (item) => item.message.role !== "system",
  );
  const msgs: ChatMessage[] = [];

  for (let i = 0; i < filteredHistory.length; i++) {
    const historyItem = filteredHistory[i];

    // 使用配置项来决定是否在Chat模式下过滤工具调用
    if (messageMode === "chat" && !(config.keepToolCallsInChatMode ?? false)) {
      const toolMessage: ToolResultChatMessage =
        historyItem.message as ToolResultChatMessage;
      if (historyItem.toolCallState?.toolCallId || toolMessage.toolCallId) {
        // remove all tool calls from the history
        continue;
      }
    }

    if (historyItem.message.role === "user") {
      // Gather context items for user messages
      let content = normalizeToMessageParts(historyItem.message);

      const ctxItems = historyItem.contextItems
        .map((ctxItem) => {
          return {
            type: "text",
            text: `${ctxItem.content}\n`,
          } as TextMessagePart;
        })
        .filter((part) => !!part.text.trim());

      content = [...ctxItems, ...content];
      msgs.push({
        ...historyItem.message,
        content,
      });
    } else {
      msgs.push(historyItem.message);
    }
  }

  const lastUserMsg = findLast(msgs, isUserOrToolMsg) as
    | UserChatMessage
    | ToolResultChatMessage
    | undefined;

  // Get context items for the last user message
  const lastUserContextItems = getUserContextItems(
    lastUserMsg,
    filteredHistory,
  );
  let systemMessage = getSystemMessageWithRules({
    baseSystemMessage: baseChatOrAgentSystemMessage,
    rules,
    userMessage: lastUserMsg,
    contextItems: lastUserContextItems,
  });

  // 读取项目根目录下的 new-coder.md 文件内容并添加到系统消息中
  if (ide) {
    const newCoderContent = await readNewCoderMd(ide);
    if (newCoderContent) {
      systemMessage = systemMessage.trim()
        ? `${systemMessage}\n\n## 项目信息\n${newCoderContent}`
        : `## 项目信息\n${newCoderContent}`;
    }
  }

  // 如果有动态系统消息，将其合并到系统消息中
  if (dynamicSystemMessage && dynamicSystemMessage.trim()) {
    systemMessage = systemMessage.trim()
      ? `${systemMessage}\n\n${dynamicSystemMessage}`
      : dynamicSystemMessage;
    // systemMessage = dynamicSystemMessage;
  }

  if (systemMessage.trim()) {
    msgs.unshift({
      role: "system",
      content: systemMessage,
    });
  }

  // Remove the "id" from all of the messages
  return msgs.map((msg) => {
    const { id, ...rest } = msg as any;
    return rest;
  });
}
