# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm run tsc:watch` - Watch mode for TypeScript compilation across all workspaces (gui, vscode, core, binary)
- `npm run format` - Format code using Prettier
- `npm run format:check` - Check code formatting without making changes

### Core Package (core/)
- `npm test` - Run Jest tests with experimental VM modules
- `npm run vitest` - Run specific vitest for LocalPlatformClient
- `npm run test:coverage` - Run tests with coverage report
- `npm run tsc:check` - TypeScript type checking without compilation
- `npm run build:npm` - Build for npm distribution
- `npm run lint` - Run ESLint
- `npm run lint:fix` - Run ESLint with auto-fix

### GUI Package (gui/)
- `npm run dev` - Start Vite development server
- `npm run build` - Build GUI for production
- `npm run test` - Run vitest tests
- `npm run test:coverage` - Run tests with coverage
- `npm run test:ui` - Run tests with UI
- `npm run test:watch` - Run tests in watch mode
- `npm run lint` - Run ESLint

### VS Code Extension (extensions/vscode/)
- `npm run esbuild` - Build extension with sourcemaps
- `npm run esbuild-watch` - Build in watch mode
- `npm run tsc:check` - TypeScript type checking
- `npm run test` - Run extension tests
- `npm run package` - Package extension as .vsix file
- `npm run lint` - Run ESLint

### IntelliJ Extension (extensions/intellij/)
- `./gradlew tasks` - List all available Gradle tasks
- `./gradlew build` - Build and test the project
- `./gradlew runIde` - Run IDE instance with plugin installed
- `./gradlew buildPlugin` - Package plugin as .zip file
- `./gradlew test` - Run tests
- `./gradlew clean` - Clean build directory

### Documentation (docs/)
- `npm install && npm run start` - Start documentation server locally

## Architecture

Continue is an AI code assistant that works across VS Code and JetBrains IDEs through extensions that communicate with a shared core.

### Key Components

**Core (`core/`)**: Shared TypeScript library containing:
- LLM providers and model integrations
- Context providers and codebase indexing
- Protocol definitions and abstractions
- Agent and workflow implementations

**GUI (`gui/`)**: React-based web interface shared across both IDE extensions:
- Chat interface and conversation management
- Settings and configuration pages
- Agent workflow visualization
- Built with React, Redux, TailwindCSS

**VS Code Extension (`extensions/vscode/`)**: 
- TypeScript extension communicating with core via direct imports
- Webview integration for GUI
- VS Code-specific IDE integrations

**IntelliJ Extension (`extensions/intellij/`)**:
- Kotlin/Java extension communicating with core via binary process
- Uses packaged binary from `binary/` directory
- JetBrains platform integrations

**Binary (`binary/`)**: Node.js executable that packages core functionality for IntelliJ extension communication over stdin/stdout.

### Communication Patterns

- **VS Code**: Direct TypeScript imports and webview messaging
- **IntelliJ**: Process-based communication with packaged binary
- **GUI**: Embedded webview in both extensions with shared React components

### Configuration

- Main config: `config.json` or `config.yaml` in `.continue/` directory
- VS Code settings: Available through VS Code settings UI
- IntelliJ settings: Through plugin settings panel

### Development Setup Notes

- **Node.js**: Requires version 20.19.0 (LTS) or higher
- **JDK**: IntelliJ extension requires JDK 17
- **IDE**: IntelliJ IDEA recommended for IntelliJ extension development
- **Debugging**: VS Code extension supports full breakpoint debugging; IntelliJ extension requires IntelliJ Ultimate for core debugging

### Testing Strategy

- **Core**: Jest unit tests and vitest for specific components
- **GUI**: Vitest with React Testing Library
- **VS Code**: Extension test framework with VS Code API
- **IntelliJ**: intellij-ui-test-robot for e2e testing

### Key Directories to Understand

- `core/llm/llms/` - LLM provider implementations
- `core/context/` - Context providers for codebase analysis
- `core/agent/` - Agent and workflow implementations
- `gui/src/pages/` - Main GUI pages and components
- `extensions/vscode/src/` - VS Code extension implementation
- `extensions/intellij/src/main/kotlin/` - IntelliJ extension implementation

### Development Workflow Tips

- Use `npm run tsc:watch` from root to catch TypeScript errors across all packages
- GUI changes hot-reload automatically in both extensions during development
- Core changes require rebuilding binary for IntelliJ testing
- Both extensions can be developed simultaneously with different IDE instances